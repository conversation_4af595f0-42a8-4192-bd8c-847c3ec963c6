import React from 'react';

const TestApp: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontSize: '24px', 
      color: 'blue',
      backgroundColor: 'lightgray',
      minHeight: '100vh'
    }}>
      <h1>🏥 HMS Test App</h1>
      <p>If you can see this, React is working!</p>
      <button onClick={() => alert('Button clicked!')}>
        Test Button
      </button>
      <div style={{ marginTop: '20px' }}>
        <h2>Available Test Users:</h2>
        <ul>
          <li><strong>Admin:</strong> admin / admin123</li>
          <li><strong>Doctor:</strong> dr.smith / doctor123</li>
          <li><strong>Patient:</strong> patient.doe / patient123</li>
          <li><strong>Nurse:</strong> nurse.mary / nurse123</li>
          <li><strong>Receptionist:</strong> receptionist / reception123</li>
        </ul>
      </div>
      <div style={{ marginTop: '20px' }}>
        <h2>Backend API:</h2>
        <p>Backend is running at: <a href="http://127.0.0.1:8000/api/" target="_blank">http://127.0.0.1:8000/api/</a></p>
        <p>Login endpoint: <a href="http://127.0.0.1:8000/api/auth/login/" target="_blank">http://127.0.0.1:8000/api/auth/login/</a></p>
      </div>
    </div>
  );
};

export default TestApp;
