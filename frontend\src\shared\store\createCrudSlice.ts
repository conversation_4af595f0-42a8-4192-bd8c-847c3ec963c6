/**
 * Generic CRUD Slice Factory
 * Creates standardized Redux slices for CRUD operations with consistent patterns
 */

import { createSlice, createAsyncThunk, PayloadAction, SliceCaseReducers } from '@reduxjs/toolkit';
import { BaseApiService } from '../services/BaseApiService';
import type {
  BaseEntity,
  PaginatedResponse,
  PaginationParams,
  FilterParams,
  LoadingState,
  ErrorState,
} from '../types/api';

// Generic state interface for CRUD operations
export interface CrudState<T extends BaseEntity> {
  items: T[];
  selectedItem: T | null;
  loading: LoadingState;
  error: ErrorState;
  pagination: PaginatedResponse<T>['pagination'] | null;
  filters: FilterParams;
  searchQuery: string;
  lastFetch: number | null;
}

// Initial state factory
const createInitialState = <T extends BaseEntity>(): CrudState<T> => ({
  items: [],
  selectedItem: null,
  loading: {
    list: false,
    create: false,
    update: false,
    delete: false,
  },
  error: {
    list: null,
    create: null,
    update: null,
    delete: null,
  },
  pagination: null,
  filters: {},
  searchQuery: '',
  lastFetch: null,
});

// Async thunk factory
export const createCrudThunks = <T extends BaseEntity>(
  name: string,
  service: BaseApiService<T>
) => {
  const fetchAll = createAsyncThunk(
    `${name}/fetchAll`,
    async (params: PaginationParams & FilterParams = {}, { rejectWithValue }) => {
      try {
        const response = await service.getAll(params);
        return response;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to fetch items');
      }
    }
  );

  const fetchById = createAsyncThunk(
    `${name}/fetchById`,
    async (id: string | number, { rejectWithValue }) => {
      try {
        const response = await service.getById(id);
        return response.data;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to fetch item');
      }
    }
  );

  const create = createAsyncThunk(
    `${name}/create`,
    async (data: Partial<T>, { rejectWithValue }) => {
      try {
        const response = await service.create(data);
        return response.data;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to create item');
      }
    }
  );

  const update = createAsyncThunk(
    `${name}/update`,
    async ({ id, data }: { id: string | number; data: Partial<T> }, { rejectWithValue }) => {
      try {
        const response = await service.update(id, data);
        return response.data;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to update item');
      }
    }
  );

  const deleteItem = createAsyncThunk(
    `${name}/delete`,
    async (id: string | number, { rejectWithValue }) => {
      try {
        await service.deleteById(id);
        return id;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to delete item');
      }
    }
  );

  const bulkDelete = createAsyncThunk(
    `${name}/bulkDelete`,
    async (ids: (string | number)[], { rejectWithValue }) => {
      try {
        await service.bulkDelete(ids);
        return ids;
      } catch (error: any) {
        return rejectWithValue(error.message || 'Failed to delete items');
      }
    }
  );

  return {
    fetchAll,
    fetchById,
    create,
    update,
    delete: deleteItem,
    bulkDelete,
  };
};

// Generic reducers factory
const createCrudReducers = <T extends BaseEntity>() => ({
  setSelectedItem: (state: CrudState<T>, action: PayloadAction<T | null>) => {
    state.selectedItem = action.payload;
  },
  
  setFilters: (state: CrudState<T>, action: PayloadAction<FilterParams>) => {
    state.filters = action.payload;
  },
  
  updateFilters: (state: CrudState<T>, action: PayloadAction<Partial<FilterParams>>) => {
    state.filters = { ...state.filters, ...action.payload };
  },
  
  clearFilters: (state: CrudState<T>) => {
    state.filters = {};
  },
  
  setSearchQuery: (state: CrudState<T>, action: PayloadAction<string>) => {
    state.searchQuery = action.payload;
  },
  
  clearError: (state: CrudState<T>, action: PayloadAction<keyof ErrorState | undefined>) => {
    if (action.payload) {
      state.error[action.payload] = null;
    } else {
      state.error = {
        list: null,
        create: null,
        update: null,
        delete: null,
      };
    }
  },
  
  reset: (state: CrudState<T>) => {
    return createInitialState<T>();
  },
  
  // Optimistic updates
  optimisticAdd: (state: CrudState<T>, action: PayloadAction<T>) => {
    state.items.unshift(action.payload);
  },
  
  optimisticUpdate: (state: CrudState<T>, action: PayloadAction<T>) => {
    const index = state.items.findIndex(item => item.id === action.payload.id);
    if (index !== -1) {
      state.items[index] = action.payload;
    }
    if (state.selectedItem?.id === action.payload.id) {
      state.selectedItem = action.payload;
    }
  },
  
  optimisticRemove: (state: CrudState<T>, action: PayloadAction<string | number>) => {
    state.items = state.items.filter(item => item.id !== action.payload);
    if (state.selectedItem?.id === action.payload) {
      state.selectedItem = null;
    }
  },
});

// Extra reducers factory for async thunks
const createExtraReducers = <T extends BaseEntity>(thunks: ReturnType<typeof createCrudThunks<T>>) => 
  (builder: any) => {
    // Fetch All
    builder
      .addCase(thunks.fetchAll.pending, (state: CrudState<T>) => {
        state.loading.list = true;
        state.error.list = null;
      })
      .addCase(thunks.fetchAll.fulfilled, (state: CrudState<T>, action: PayloadAction<PaginatedResponse<T>>) => {
        state.loading.list = false;
        state.items = action.payload.data;
        if ('pagination' in action.payload) {
          state.pagination = action.payload.pagination;
        }
        state.lastFetch = Date.now();
      })
      .addCase(thunks.fetchAll.rejected, (state: CrudState<T>, action) => {
        state.loading.list = false;
        state.error.list = action.payload as string;
      });

    // Fetch By ID
    builder
      .addCase(thunks.fetchById.pending, (state: CrudState<T>) => {
        state.loading.list = true;
        state.error.list = null;
      })
      .addCase(thunks.fetchById.fulfilled, (state: CrudState<T>, action: PayloadAction<T>) => {
        state.loading.list = false;
        state.selectedItem = action.payload;
        
        // Update item in list if it exists
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
      })
      .addCase(thunks.fetchById.rejected, (state: CrudState<T>, action) => {
        state.loading.list = false;
        state.error.list = action.payload as string;
      });

    // Create
    builder
      .addCase(thunks.create.pending, (state: CrudState<T>) => {
        state.loading.create = true;
        state.error.create = null;
      })
      .addCase(thunks.create.fulfilled, (state: CrudState<T>, action: PayloadAction<T>) => {
        state.loading.create = false;
        state.items.unshift(action.payload);
        state.selectedItem = action.payload;
      })
      .addCase(thunks.create.rejected, (state: CrudState<T>, action) => {
        state.loading.create = false;
        state.error.create = action.payload as string;
      });

    // Update
    builder
      .addCase(thunks.update.pending, (state: CrudState<T>) => {
        state.loading.update = true;
        state.error.update = null;
      })
      .addCase(thunks.update.fulfilled, (state: CrudState<T>, action: PayloadAction<T>) => {
        state.loading.update = false;
        
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
        
        if (state.selectedItem?.id === action.payload.id) {
          state.selectedItem = action.payload;
        }
      })
      .addCase(thunks.update.rejected, (state: CrudState<T>, action) => {
        state.loading.update = false;
        state.error.update = action.payload as string;
      });

    // Delete
    builder
      .addCase(thunks.delete.pending, (state: CrudState<T>) => {
        state.loading.delete = true;
        state.error.delete = null;
      })
      .addCase(thunks.delete.fulfilled, (state: CrudState<T>, action: PayloadAction<string | number>) => {
        state.loading.delete = false;
        state.items = state.items.filter(item => item.id !== action.payload);
        
        if (state.selectedItem?.id === action.payload) {
          state.selectedItem = null;
        }
      })
      .addCase(thunks.delete.rejected, (state: CrudState<T>, action) => {
        state.loading.delete = false;
        state.error.delete = action.payload as string;
      });

    // Bulk Delete
    builder
      .addCase(thunks.bulkDelete.pending, (state: CrudState<T>) => {
        state.loading.delete = true;
        state.error.delete = null;
      })
      .addCase(thunks.bulkDelete.fulfilled, (state: CrudState<T>, action: PayloadAction<(string | number)[]>) => {
        state.loading.delete = false;
        state.items = state.items.filter(item => !action.payload.includes(item.id));
        
        if (state.selectedItem && action.payload.includes(state.selectedItem.id)) {
          state.selectedItem = null;
        }
      })
      .addCase(thunks.bulkDelete.rejected, (state: CrudState<T>, action) => {
        state.loading.delete = false;
        state.error.delete = action.payload as string;
      });
  };

// Main slice factory
export const createCrudSlice = <T extends BaseEntity>(
  name: string,
  service: BaseApiService<T>,
  additionalReducers?: SliceCaseReducers<CrudState<T>>
) => {
  const thunks = createCrudThunks(name, service);
  const initialState = createInitialState<T>();
  
  const slice = createSlice({
    name,
    initialState,
    reducers: {
      ...createCrudReducers<T>(),
      ...additionalReducers,
    },
    extraReducers: createExtraReducers(thunks),
  });

  return {
    slice,
    actions: slice.actions,
    reducer: slice.reducer,
    thunks,
  };
};
