#!/usr/bin/env python3
"""
Comprehensive HMS API End-to-End Testing Script
Tests all user roles and major functionality
"""

import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

# API Configuration
BASE_URL = 'http://127.0.0.1:8000/api'
HEADERS = {'Content-Type': 'application/json'}

class APITester:
    def __init__(self):
        self.tokens = {}
        self.users = {}
        
    def print_header(self, title):
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
        
    def print_test(self, test_name, status, details=""):
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {test_name}")
        if details:
            print(f"   {details}")
            
    def login_user(self, username, password, role_name):
        """Login and store tokens for a user"""
        self.print_header(f"Testing {role_name} Login")
        
        try:
            response = requests.post(
                f"{BASE_URL}/auth/login/",
                headers=HEADERS,
                data=json.dumps({"username": username, "password": password})
            )
            
            if response.status_code == 200:
                data = response.json()
                self.tokens[role_name] = data['tokens']['access']
                self.users[role_name] = data['user']
                self.print_test(f"{role_name} Login", True, f"User ID: {data['user']['id']}")
                return True
            else:
                self.print_test(f"{role_name} Login", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_test(f"{role_name} Login", False, f"Error: {str(e)}")
            return False
    
    def test_authenticated_endpoint(self, role_name, endpoint, method="GET", data=None):
        """Test an endpoint with authentication"""
        if role_name not in self.tokens:
            return False
            
        headers = {
            **HEADERS,
            'Authorization': f'Bearer {self.tokens[role_name]}'
        }
        
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            elif method == "POST":
                response = requests.post(f"{BASE_URL}{endpoint}", headers=headers, data=json.dumps(data))
            elif method == "PUT":
                response = requests.put(f"{BASE_URL}{endpoint}", headers=headers, data=json.dumps(data))
            elif method == "DELETE":
                response = requests.delete(f"{BASE_URL}{endpoint}", headers=headers)
            
            success = response.status_code in [200, 201, 204]
            details = f"Status: {response.status_code}"
            
            if success and response.content:
                try:
                    result = response.json()
                    if isinstance(result, dict):
                        if 'results' in result:
                            details += f", Items: {len(result['results'])}"
                        elif 'count' in result:
                            details += f", Count: {result['count']}"
                except:
                    pass
                    
            self.print_test(f"{role_name} - {method} {endpoint}", success, details)
            return success, response
            
        except Exception as e:
            self.print_test(f"{role_name} - {method} {endpoint}", False, f"Error: {str(e)}")
            return False, None
    
    def test_all_users_login(self):
        """Test login for all user roles"""
        test_users = [
            ("admin", "admin123", "Admin"),
            ("dr.smith", "doctor123", "Doctor"),
            ("patient.doe", "patient123", "Patient"),
            ("nurse.mary", "nurse123", "Nurse"),
            ("receptionist", "reception123", "Receptionist")
        ]
        
        for username, password, role in test_users:
            self.login_user(username, password, role)
    
    def test_patient_endpoints(self):
        """Test patient-related endpoints"""
        self.print_header("Patient Management Testing")
        
        # Test with Admin access
        self.test_authenticated_endpoint("Admin", "/patients/patients/")
        self.test_authenticated_endpoint("Doctor", "/patients/patients/")
        self.test_authenticated_endpoint("Patient", "/patients/patients/")
        
        # Test patient profile access
        if "Patient" in self.users:
            patient_id = self.users["Patient"]["id"]
            self.test_authenticated_endpoint("Patient", f"/patients/patients/{patient_id}/")
    
    def test_appointment_endpoints(self):
        """Test appointment-related endpoints"""
        self.print_header("Appointment System Testing")
        
        self.test_authenticated_endpoint("Admin", "/appointments/appointments/")
        self.test_authenticated_endpoint("Doctor", "/appointments/appointments/")
        self.test_authenticated_endpoint("Patient", "/appointments/appointments/")
        
        # Test appointment creation
        if "Patient" in self.tokens and "Doctor" in self.users:
            appointment_data = {
                "doctor": self.users["Doctor"]["id"],
                "appointment_date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
                "appointment_time": "10:00:00",
                "reason": "Regular checkup",
                "notes": "Test appointment"
            }
            self.test_authenticated_endpoint("Patient", "/appointments/appointments/", "POST", appointment_data)
    
    def test_medical_records(self):
        """Test medical records endpoints"""
        self.print_header("Medical Records Testing")
        
        self.test_authenticated_endpoint("Admin", "/patients/medical-records/")
        self.test_authenticated_endpoint("Doctor", "/patients/medical-records/")
        self.test_authenticated_endpoint("Patient", "/patients/medical-records/")
    
    def test_billing_endpoints(self):
        """Test billing system endpoints"""
        self.print_header("Billing System Testing")
        
        self.test_authenticated_endpoint("Admin", "/billing/bills/")
        self.test_authenticated_endpoint("Doctor", "/billing/bills/")
        self.test_authenticated_endpoint("Patient", "/billing/bills/")
    
    def test_ai_endpoints(self):
        """Test AI service endpoints"""
        self.print_header("AI Services Testing")
        
        self.test_authenticated_endpoint("Admin", "/ai/")
        self.test_authenticated_endpoint("Doctor", "/ai/")
    
    def test_user_profile_endpoints(self):
        """Test user profile management"""
        self.print_header("User Profile Testing")
        
        self.test_authenticated_endpoint("Admin", "/auth/profile/")
        self.test_authenticated_endpoint("Doctor", "/auth/profile/")
        self.test_authenticated_endpoint("Patient", "/auth/profile/")
        self.test_authenticated_endpoint("Nurse", "/auth/profile/")
        self.test_authenticated_endpoint("Receptionist", "/auth/profile/")
    
    def run_comprehensive_test(self):
        """Run all tests"""
        print("🏥 HMS COMPREHENSIVE API TESTING")
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test authentication for all users
        self.test_all_users_login()
        
        # Test core functionality
        self.test_user_profile_endpoints()
        self.test_patient_endpoints()
        self.test_appointment_endpoints()
        self.test_medical_records()
        self.test_billing_endpoints()
        self.test_ai_endpoints()
        
        # Summary
        self.print_header("Test Summary")
        print(f"✅ Logged in users: {len(self.tokens)}")
        print(f"🔑 Available tokens: {list(self.tokens.keys())}")
        print(f"👥 User details: {list(self.users.keys())}")
        
        print(f"\n🎯 TESTING COMPLETE")
        print(f"Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    tester = APITester()
    tester.run_comprehensive_test()
