import { BaseApiService } from '../shared/services/BaseApiService';
import type { BaseEntity } from '../shared/types/api';

export interface Invoice extends BaseEntity {
  invoice_id: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      id: number;
      full_name: string;
      email: string;
    };
  };
  appointment?: {
    id: number;
    appointment_id: string;
    appointment_date: string;
    appointment_time: string;
  };
  invoice_date: string;
  due_date: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  status: string;
  notes?: string;
  items: InvoiceItem[];
  balance_due: number;
  is_paid: boolean;
}

export interface InvoiceItem extends BaseEntity {
  description: string;
  item_type: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface Payment extends BaseEntity {
  payment_id: string;
  invoice: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      id: number;
      full_name: string;
    };
  };
  amount: number;
  payment_method: string;
  payment_date: string;
  reference_number?: string;
  notes?: string;
  status: string;
}

export interface InsuranceClaim extends BaseEntity {
  claim_id: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      id: number;
      full_name: string;
    };
  };
  invoice: string;
  insurance_provider: string;
  policy_number: string;
  claim_amount: number;
  approved_amount: number;
  submitted_date: string;
  processed_date?: string;
  status: string;
  denial_reason?: string;
  notes?: string;
}

class BillingService extends BaseApiService<Invoice> {
  constructor() {
    super({
      baseURL: '/billing',
      endpoints: {
        list: '/invoices/',
        detail: '/invoices/:id/',
        create: '/invoices/',
        update: '/invoices/:id/',
        delete: '/invoices/:id/',
      },
    });
  }

  // Custom invoice actions
  async markInvoicePaid(id: number) {
    return this.customAction(id, 'mark_paid');
  }

  async markInvoiceOverdue(id: number) {
    return this.customAction(id, 'mark_overdue');
  }

  async getOverdueInvoices() {
    return this.get('/invoices/overdue/');
  }

  async getUnpaidInvoices() {
    return this.get('/invoices/unpaid/');
  }

  async getBillingSummary() {
    return this.get('/invoices/summary/');
  }

  // Invoice Items - using base service methods
  async getInvoiceItems(params?: any) {
    return this.get('/invoice-items/', { params });
  }

  async createInvoiceItem(data: any) {
    return this.post('/invoice-items/', data);
  }

  async updateInvoiceItem(id: number, data: any) {
    return this.patch('/invoice-items/:id/', data, { params: { id } });
  }

  async deleteInvoiceItem(id: number) {
    return this.delete('/invoice-items/:id/', { params: { id } });
  }

  // Payments - using base service methods
  async getPayments(params?: any) {
    return this.get('/payments/', { params });
  }

  async getPayment(id: number) {
    return this.get('/payments/:id/', { params: { id } });
  }

  async createPayment(data: any) {
    return this.post('/payments/', data);
  }

  async updatePayment(id: number, data: any) {
    return this.patch('/payments/:id/', data, { params: { id } });
  }

  async deletePayment(id: number) {
    return this.delete('/payments/:id/', { params: { id } });
  }

  async getTodayPayments() {
    return this.get('/payments/today/');
  }

  // Insurance Claims - using base service methods
  async getInsuranceClaims(params?: any) {
    return this.get('/insurance-claims/', { params });
  }

  async getInsuranceClaim(id: number) {
    return this.get('/insurance-claims/:id/', { params: { id } });
  }

  async createInsuranceClaim(data: any) {
    return this.post('/insurance-claims/', data);
  }

  async updateInsuranceClaim(id: number, data: any) {
    return this.patch('/insurance-claims/:id/', data, { params: { id } });
  }

  async deleteInsuranceClaim(id: number) {
    return this.delete('/insurance-claims/:id/', { params: { id } });
  }

  async approveInsuranceClaim(id: number, approvedAmount?: number) {
    const data = approvedAmount ? { approved_amount: approvedAmount } : {};
    return this.post('/insurance-claims/:id/approve/', data, { params: { id } });
  }

  async denyInsuranceClaim(id: number, denialReason: string) {
    return this.post('/insurance-claims/:id/deny/', {
      denial_reason: denialReason
    }, { params: { id } });
  }
}

export default new BillingService();
